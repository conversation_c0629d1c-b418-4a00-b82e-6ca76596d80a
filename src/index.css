* {
  box-sizing: border-box;
}

:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #fafafa;
}

#root {
  min-height: 100vh;
}

/* Reset default button styles */
button {
  font-family: inherit;
  cursor: pointer;
}

/* Reset default input styles */
input {
  font-family: inherit;
}
