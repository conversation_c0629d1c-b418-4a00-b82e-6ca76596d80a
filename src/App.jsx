import { useState, useRef, useEffect } from 'react'
import './App.css'

// Utility function to copy text to clipboard
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (err) {
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    return true
  }
}

// Component to render message content with code block support
const MessageContent = ({ content }) => {
  const [copiedStates, setCopiedStates] = useState({})

  const handleCopy = async (code, index) => {
    const success = await copyToClipboard(code)
    if (success) {
      setCopiedStates(prev => ({ ...prev, [index]: true }))
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [index]: false }))
      }, 2000)
    }
  }

  // Simple code block detection and rendering
  const renderContent = () => {
    // Split content by code blocks (looking for ``` patterns)
    const parts = content.split(/(```[\s\S]*?```|`[^`]+`)/g)

    return parts.map((part, index) => {
      if (part.startsWith('```') && part.endsWith('```')) {
        // Multi-line code block
        const code = part.slice(3, -3).trim()
        const lines = code.split('\n')
        const language = lines[0].match(/^[a-zA-Z]+$/) ? lines.shift() : ''
        const codeContent = lines.join('\n')

        return (
          <div key={index} className="code-block-container">
            <pre>
              <button
                className="copy-button"
                onClick={() => handleCopy(codeContent, index)}
                title="Copy code"
              >
                <svg className="copy-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                </svg>
                {copiedStates[index] ? 'Copied!' : 'Copy'}
              </button>
              <code>{codeContent}</code>
            </pre>
          </div>
        )
      } else if (part.startsWith('`') && part.endsWith('`')) {
        // Inline code
        const code = part.slice(1, -1)
        return <code key={index}>{code}</code>
      } else {
        // Regular text
        return <span key={index}>{part}</span>
      }
    })
  }

  return <div>{renderContent()}</div>
}

function App() {
  const [inputValue, setInputValue] = useState('')
  const [messages, setMessages] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [error, setError] = useState('')
  const messagesEndRef = useRef(null)
  const mediaRecorderRef = useRef(null)
  const recognitionRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    // Initialize speech recognition
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInputValue(transcript);
        setIsRecording(false);
      };

      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsRecording(false);
        setError('Speech recognition failed. Please try again.');
      };

      recognitionRef.current.onend = () => {
        setIsRecording(false);
      };
    }
  }, []);

  const handleInputChange = (e) => {
    setInputValue(e.target.value)
    setError('')
  }

  const handleMicClick = () => {
    if (!recognitionRef.current) {
      setError('Speech recognition is not supported in your browser.');
      return;
    }

    if (isRecording) {
      recognitionRef.current.stop();
      setIsRecording(false);
    } else {
      setIsRecording(true);
      setError('');
      recognitionRef.current.start();
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!inputValue.trim() || isLoading) return

    const userMessage = inputValue.trim()
    setInputValue('')

    // Add user message to chat
    setMessages(prev => [...prev, { role: 'user', content: userMessage }])
    setIsLoading(true)

    try {
      // Add AI message placeholder
      setMessages(prev => [...prev, { role: 'assistant', content: '', isStreaming: true }])

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: userMessage }),
      })

      if (!response.ok) {
        throw new Error('Failed to get response')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              setMessages(prev => prev.map((msg, index) =>
                index === prev.length - 1 ? { ...msg, isStreaming: false } : msg
              ))
              break
            }

            try {
              const parsed = JSON.parse(data)
              if (parsed.text) {
                setMessages(prev => prev.map((msg, index) =>
                  index === prev.length - 1
                    ? { ...msg, content: msg.content + parsed.text }
                    : msg
                ))
              }
            } catch (e) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        }
      }
    } catch (error) {
      console.error('Error:', error)
      setMessages(prev => prev.map((msg, index) =>
        index === prev.length - 1
          ? { ...msg, content: 'Sorry, there was an error processing your request.', isStreaming: false }
          : msg
      ))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="app">
      <div className="container">
        {messages.length === 0 ? (
          <div className="header">
            <img
              src="/logo.svg"
              alt="Logo"
              className="logo"
            />
            <h1 className="title">What can I help with?</h1>
          </div>
        ) : (
          <div className="messages-container">
            <div className="chat-header">
              <span className="chat-title">AI Chat</span>
            </div>
            {messages.map((message, index) => (
              <div key={index} className={`message ${message.role}`}>
                <div className="message-content">
                  {message.role === 'user' ? (
                    <div className="user-message">
                      <div className="user-avatar">
                        <span>You</span>
                      </div>
                      <div className="message-text">{message.content}</div>
                    </div>
                  ) : (
                    <div className="assistant-message">
                      <div className="assistant-avatar">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <circle cx="12" cy="12" r="3"/>
                          <path d="M12 1v6m0 6v6"/>
                          <path d="m21 12-6-3-6 3-6-3"/>
                        </svg>
                      </div>
                      <div className="message-text">
                        {message.content}
                        {message.isStreaming && <span className="cursor">|</span>}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}

        {error && (
          <div className="error-message">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="input-form">
          <div className="input-container">
            <button type="button" className="tools-button">
              <svg className="tools-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
              </svg>
              Tools
            </button>

            <input
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              placeholder={isRecording ? "Listening..." : "Search anything"}
              className="main-input"
              disabled={isLoading || isRecording}
            />

            <div className="right-controls">
              <button
                type="button"
                className={`mic-button ${isRecording ? 'recording' : ''}`}
                onClick={handleMicClick}
                disabled={isLoading}
                title={isRecording ? "Stop recording" : "Start voice input"}
              >
                {isRecording ? (
                  <svg className="mic-icon" viewBox="0 0 24 24" fill="currentColor">
                    <rect x="6" y="6" width="12" height="12" rx="2"/>
                  </svg>
                ) : (
                  <svg className="mic-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                    <line x1="12" y1="19" x2="12" y2="23"/>
                    <line x1="8" y1="23" x2="16" y2="23"/>
                  </svg>
                )}
              </button>

              {inputValue.trim() && !isLoading ? (
                <button
                  type="submit"
                  className="send-button"
                  title="Send message"
                >
                  <svg className="send-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <line x1="22" y1="2" x2="11" y2="13"/>
                    <polygon points="22,2 15,22 11,13 2,9"/>
                  </svg>
                </button>
              ) : (
                <div className="avatar">
                  <span className="avatar-text">AI</span>
                </div>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}

export default App
