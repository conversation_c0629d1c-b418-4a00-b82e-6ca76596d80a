import React, { useState } from 'react'
import './MessageContent.css'

const MessageContent = ({ content }) => {
  const [copiedBlocks, setCopiedBlocks] = useState(new Set())

  const copyToClipboard = async (text, blockIndex) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedBlocks(prev => new Set([...prev, blockIndex]))
      setTimeout(() => {
        setCopiedBlocks(prev => {
          const newSet = new Set(prev)
          newSet.delete(blockIndex)
          return newSet
        })
      }, 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  const formatContent = (text) => {
    if (!text) return []

    const parts = []
    let currentIndex = 0
    let blockIndex = 0

    // Split by code blocks (```...```)
    const codeBlockRegex = /```(\w+)?\n?([\s\S]*?)```/g
    let match

    while ((match = codeBlockRegex.exec(text)) !== null) {
      // Add text before code block
      if (match.index > currentIndex) {
        const beforeText = text.slice(currentIndex, match.index)
        parts.push(...formatTextContent(beforeText))
      }

      // Add code block
      const language = match[1] || 'text'
      const code = match[2].trim()
      const currentBlockIndex = blockIndex++

      parts.push(
        <div key={`code-${currentBlockIndex}`} className="code-block">
          <div className="code-header">
            <span className="code-language">{language}</span>
            <button
              className="copy-button"
              onClick={() => copyToClipboard(code, currentBlockIndex)}
              title="Copy code"
            >
              {copiedBlocks.has(currentBlockIndex) ? (
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="20,6 9,17 4,12"/>
                </svg>
              ) : (
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                  <path d="M5,15H4a2,2,0,0,1-2-2V4A2,2,0,0,1,4,2H13a2,2,0,0,1,2,2V5"/>
                </svg>
              )}
            </button>
          </div>
          <pre className="code-content">
            <code>{code}</code>
          </pre>
        </div>
      )

      currentIndex = match.index + match[0].length
    }

    // Add remaining text
    if (currentIndex < text.length) {
      const remainingText = text.slice(currentIndex)
      parts.push(...formatTextContent(remainingText))
    }

    return parts
  }

  const formatTextContent = (text) => {
    if (!text.trim()) return []

    const parts = []
    const lines = text.split('\n')
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]
      
      if (line.trim()) {
        // Format inline code (`code`)
        const inlineCodeRegex = /`([^`]+)`/g
        const lineParts = []
        let lastIndex = 0
        let match

        while ((match = inlineCodeRegex.exec(line)) !== null) {
          // Add text before inline code
          if (match.index > lastIndex) {
            lineParts.push(line.slice(lastIndex, match.index))
          }
          
          // Add inline code
          lineParts.push(
            <code key={`inline-${i}-${match.index}`} className="inline-code">
              {match[1]}
            </code>
          )
          
          lastIndex = match.index + match[0].length
        }

        // Add remaining text
        if (lastIndex < line.length) {
          lineParts.push(line.slice(lastIndex))
        }

        parts.push(
          <div key={`line-${i}`} className="text-line">
            {lineParts.length > 0 ? lineParts : line}
          </div>
        )
      } else if (i < lines.length - 1) {
        // Add empty line (but not at the end)
        parts.push(<div key={`empty-${i}`} className="text-line empty-line"></div>)
      }
    }

    return parts
  }

  const formattedContent = formatContent(content)

  return (
    <div className="message-content-wrapper">
      {formattedContent}
    </div>
  )
}

export default MessageContent
