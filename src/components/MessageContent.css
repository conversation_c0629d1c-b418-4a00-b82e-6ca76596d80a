.message-content-wrapper {
  width: 100%;
  line-height: 1.6;
  color: #333;
}

.text-line {
  margin-bottom: 0.5em;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.text-line:last-child {
  margin-bottom: 0;
}

.empty-line {
  height: 1em;
  margin-bottom: 0.5em;
}

.inline-code {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 3px;
  padding: 2px 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  color: #d63384;
}

.code-block {
  margin: 1em 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  background-color: #000;
  color: #fff;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #1a1a1a;
  border-bottom: 1px solid #333;
}

.code-language {
  font-size: 0.8em;
  color: #888;
  text-transform: uppercase;
  font-weight: 500;
}

.copy-button {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.copy-button:hover {
  color: #fff;
  background-color: #333;
}

.copy-button svg {
  width: 16px;
  height: 16px;
}

.code-content {
  margin: 0;
  padding: 16px;
  background-color: #000;
  color: #fff;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre;
}

.code-content code {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
}

/* Hide scrollbar for code blocks as per user preference */
.code-content::-webkit-scrollbar {
  display: none;
}

.code-content {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Dark theme adjustments for better contrast */
@media (prefers-color-scheme: dark) {
  .message-content-wrapper {
    color: #e0e0e0;
  }
  
  .inline-code {
    background-color: #2a2a2a;
    border-color: #444;
    color: #ff6b9d;
  }
  
  .code-block {
    border-color: #444;
  }
}
