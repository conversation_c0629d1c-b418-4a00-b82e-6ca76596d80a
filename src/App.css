.app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ededed;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.container {
  width: 100%;
  max-width: 700px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  gap: 1rem;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: auto 0;
  gap: 1.5rem;
}

.logo {
  width: 60px;
  height: 60px;
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.title {
  font-size: 2rem;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  margin: 0;
  font-family: 'Poppins', sans-serif;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 0;
  border-bottom: 1px solid #ddd;
  margin-bottom: 1rem;
}



.chat-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
  font-family: 'Poppins', sans-serif;
}

.message {
  width: 100%;
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.user-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  justify-content: flex-end;
  flex-direction: row-reverse;
}

.assistant-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #007aff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-avatar span {
  color: white;
  font-size: 11px;
  font-weight: 600;
}

.assistant-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.assistant-avatar svg {
  width: 16px;
  height: 16px;
  color: #666;
}

.message-text {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  max-width: 70%;
  word-wrap: break-word;
  line-height: 1.4;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: 'Poppins', sans-serif;
}

.user .message-text {
  background: #007aff;
  color: white;
}

.assistant .message-text {
  background: #f8f9fa;
  color: #1a1a1a;
}

.cursor {
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.input-form {
  width: 100%;
  flex-shrink: 0;
}

.input-container {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.input-container:focus-within {
  border-color: #007aff;
  box-shadow: 0 2px 12px rgba(0, 122, 255, 0.15);
}

.tools-button {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  padding: 8px 12px;
  border-radius: 8px;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-family: 'Poppins', sans-serif;
}

.tools-button:hover {
  background-color: #f5f5f5;
  color: #333;
}

.tools-icon {
  width: 16px;
  height: 16px;
}

.main-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 16px;
  font-size: 16px;
  background: transparent;
  color: #1a1a1a;
  font-family: 'Poppins', sans-serif;
}

.main-input::placeholder {
  color: #999;
}

.right-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mic-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mic-button:hover {
  background-color: #f5f5f5;
  color: #333;
}

.mic-button.recording {
  background-color: #ff3b30;
  color: white;
  animation: pulse 1.5s infinite;
}

.mic-button.recording:hover {
  background-color: #d70015;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.mic-icon {
  width: 18px;
  height: 18px;
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #007aff;
  border: none;
  padding: 8px;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
}

.send-button:hover {
  background-color: #0056b3;
  transform: scale(1.05);
}

.send-button:active {
  transform: scale(0.95);
}

.send-icon {
  width: 16px;
  height: 16px;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 1rem;
  border: 1px solid #fcc;
  font-size: 14px;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
}

.avatar-text {
  color: white;
  font-size: 12px;
  font-weight: 600;
}

/* Responsive design */
@media (max-width: 640px) {
  .container {
    padding: 1rem;
    height: 100vh;
  }

  .title {
    font-size: 1.5rem;
  }

  .tools-button {
    padding: 6px 8px;
    font-size: 13px;
  }

  .main-input {
    padding: 10px 12px;
    font-size: 15px;
  }

  .message-text {
    max-width: 85%;
    padding: 10px 14px;
  }

  .messages-container {
    padding: 0.5rem 0;
  }
}
