# Chat UI with Google Gemini AI

A modern React chat interface powered by Google's Gemini AI model with real-time streaming responses.

## Features

- 🎨 Clean, modern UI design
- 💬 Real-time streaming chat responses
- 🤖 Powered by Google Gemini 1.5 Flash
- 🎤 Voice input with speech recognition
- 📤 Smart send button (appears when typing)
- 📱 Responsive design for mobile and desktop
- ⚡ Fast and lightweight
- 🔒 Secure API key handling

## Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Get Your Gemini API Key
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the API key

### 3. Configure Environment Variables
1. Open the `.env` file in the project root
2. Replace `your_gemini_api_key_here` with your actual API key:
```
GEMINI_API_KEY=your_actual_api_key_here
```

### 4. Start the Application
You need to run two commands in separate terminals:

**Terminal 1 - Start the API server:**
```bash
npm start
```

**Terminal 2 - Start the frontend:**
```bash
npm run dev
```

### 5. Open the Application
Open your browser and go to: `http://localhost:5173`

## Usage

1. **Text Input**: Type your message in the input field
2. **Send**: Press Enter or click the send button (📤) that appears when typing
3. **Voice Input**: Click the microphone button (🎤) to use speech recognition
4. **Real-time Response**: Watch as the AI responds in real-time with streaming text
5. **Continue**: Keep the conversation going naturally

### Voice Input
- Click the microphone button to start voice recording
- Speak your message clearly
- The button will turn red and pulse while recording
- Click again to stop, or it will auto-stop when you finish speaking
- Your speech will be converted to text automatically

## Architecture

- **Frontend**: React with Vite for fast development
- **Backend**: Express.js server for secure API key handling
- **AI Model**: Google Gemini 1.5 Flash for intelligent responses
- **Streaming**: Server-Sent Events for real-time response streaming

## Security Note

The API key is kept secure on the backend server and never exposed to the frontend, ensuring your credentials remain protected.
