#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { GoogleGenAI } from '@google/genai';

dotenv.config();

// Validate API key at startup
if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your_gemini_api_key_here') {
  console.error('❌ GEMINI_API_KEY is not set properly. Please fix your .env file.');
  console.error('   Get your API key from: https://aistudio.google.com/app/apikey');
  process.exit(1);
}

const app = express();
const port = 3001;

// Enhanced CORS configuration
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST'],
  allowedHeaders: ['Content-Type'],
}));
app.use(express.json());

// Initialize Google AI
const ai = new GoogleGenAI({
  apiKey: process.env.GEMINI_API_KEY,
});

// Extract system prompt to a separate function
function buildSystemPrompt(message) {
  const now = new Date().toLocaleString('en-US', { timeZone: 'UTC' });
  return `You are the person who asked to run the software. Answer as a Linux expert. (${now}) Good morning! I can write code, provide code snippets, and offer examples, but I cannot execute or debug code directly on your system. Please be specific with your requests:
- What language? (e.g., C, C++, Python, Bash, Go, Rust, etc.)
- What operating system? (e.g., Ubuntu 24.04, Fedora 39, Alpine Linux)
- What is the purpose of the code?
- What inputs and outputs?
- Any specific libraries or functions?
- Any error handling requirements?
The more detail you provide, the better I can help. For complex tasks, I may give you a structured outline and code snippets to assemble. Let's get started! User question: ${message}`;
}

// API Routes
app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;

    // Enhanced message validation
    if (typeof message !== 'string' || message.trim() === '') {
      return res.status(400).json({ error: 'A non-empty message string is required' });
    }

    const config = {
      responseMimeType: 'text/plain',
    };

    const model = 'gemini-1.5-flash';
    const systemPrompt = buildSystemPrompt(message.trim());

    const contents = [
      {
        role: 'user',
        parts: [
          {
            text: systemPrompt,
          },
        ],
      },
    ];

    // Handle client disconnect
    req.on('close', () => {
      console.log('Client disconnected during streaming.');
      if (!res.headersSent) {
        res.end();
      }
    });

    // Set up Server-Sent Events for streaming
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    try {
      const response = await ai.models.generateContentStream({
        model,
        config,
        contents,
      });

      for await (const chunk of response) {
        if (chunk.text && !res.destroyed) {
          res.write(`data: ${JSON.stringify({ text: chunk.text })}\n\n`);
        }
      }

      if (!res.destroyed) {
        res.write('data: [DONE]\n\n');
        res.end();
      }
    } catch (streamError) {
      console.error('Streaming error:', streamError);
      if (!res.destroyed && !res.headersSent) {
        res.status(500).json({ error: 'Streaming error: ' + streamError.message });
      } else if (!res.destroyed) {
        res.write(`data: ${JSON.stringify({ error: 'Streaming interrupted' })}\n\n`);
        res.end();
      }
    }

  } catch (error) {
    console.error('Error:', error);
    if (!res.headersSent) {
      res.status(500).json({ error: 'Internal server error: ' + error.message });
    }
  }
});

app.listen(port, () => {
  console.log(`🚀 API Server running at http://localhost:${port}`);
  console.log('✅ GEMINI_API_KEY is properly configured');
  console.log('');
  console.log('Ready to serve AI chat requests!');
});
