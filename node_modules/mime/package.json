{"author": {"name": "<PERSON>", "url": "http://github.com/broofa", "email": "<EMAIL>"}, "type": "module", "engines": {"node": ">=16"}, "main": "./dist/src/index.js", "exports": {".": "./dist/src/index.js", "./lite": "./dist/src/index_lite.js", "./types/standard.js": "./dist/types/standard.js", "./types/other.js": "./dist/types/other.js", "./package.json": "./package.json"}, "files": ["bin", "dist/src", "dist/types", "src", "types"], "bin": {"mime": "bin/cli.js"}, "contributors": [], "description": "A comprehensive library for mime-type mapping", "license": "MIT", "devDependencies": {"@types/mime-db": "1.*", "@types/mime-types": "2.1.4", "@types/node": "22.14.0", "@typescript-eslint/eslint-plugin": "8.29.0", "@typescript-eslint/parser": "8.29.0", "chalk": "5.4.1", "mime-score": "2.0.4", "mime-types": "3.0.1", "prettier": "3.5.3", "release-please": "17.0.0", "runmd": "1.4.1", "typescript": "5.8.2"}, "scripts": {"build": "rm -fr dist && tsc", "build:types": "node dist/scripts/build.js", "build:watch": "npm run build -- --watch", "lint": "prettier -c .", "lint:fix": "prettier -w .", "prepack": "npm run build", "pretest": "npm run build", "prepublishOnly": "npm test", "test": "node --test && ./test/exports_test.sh", "test:watch": "clear && node --enable-source-maps --test --watch test"}, "keywords": ["extension", "file", "mime", "mime-db", "mimetypes", "util"], "name": "mime", "repository": {"url": "https://github.com/broofa/mime", "type": "git"}, "version": "4.0.7", "funding": ["https://github.com/sponsors/broofa"], "packageManager": "npm@11.2.0"}